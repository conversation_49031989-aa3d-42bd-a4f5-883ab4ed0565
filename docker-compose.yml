services:
  xianyu-app:
    build:
      context: .
      dockerfile: Dockerfile
    image: xianyu-auto-reply:latest
    container_name: xianyu-auto-reply
    restart: unless-stopped
    # 使用root用户避免权限问题
    user: "0:0"
    ports:
      - "${WEB_PORT:-8080}:8080"
    volumes:
      # 数据持久化
      - ./data:/app/data:rw
      - ./logs:/app/logs:rw
      - ./global_config.yml:/app/global_config.yml:ro
      # 可选：如果需要自定义配置
      # - ./custom_config.yml:/app/global_config.yml:ro
      # 可选：备份目录
      - ./backups:/app/backups:rw
    environment:
      - PYTHONUNBUFFERED=${PYTHONUNBUFFERED:-1}
      - PYTHONDONTWRITEBYTECODE=${PYTHONDONTWRITEBYTECODE:-1}
      - TZ=${TZ:-Asia/Shanghai}
      - DB_PATH=${DB_PATH:-/app/data/xianyu_data.db}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - DEBUG=${DEBUG:-false}
      - RELOAD=${RELOAD:-false}
      # SQL日志配置（默认启用，可通过环境变量覆盖）
      - SQL_LOG_ENABLED=${SQL_LOG_ENABLED:-true}
      - SQL_LOG_LEVEL=${SQL_LOG_LEVEL:-INFO}
      - ADMIN_USERNAME=${ADMIN_USERNAME:-admin}
      - ADMIN_PASSWORD=${ADMIN_PASSWORD:-admin123}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-default-secret-key}
      - SESSION_TIMEOUT=${SESSION_TIMEOUT:-3600}
      # 多用户系统配置
      - MULTIUSER_ENABLED=${MULTIUSER_ENABLED:-true}
      - USER_REGISTRATION_ENABLED=${USER_REGISTRATION_ENABLED:-true}
      - EMAIL_VERIFICATION_ENABLED=${EMAIL_VERIFICATION_ENABLED:-true}
      - CAPTCHA_ENABLED=${CAPTCHA_ENABLED:-true}
      - TOKEN_EXPIRE_TIME=${TOKEN_EXPIRE_TIME:-86400}
      - AUTO_REPLY_ENABLED=${AUTO_REPLY_ENABLED:-true}
      - AUTO_DELIVERY_ENABLED=${AUTO_DELIVERY_ENABLED:-true}
      - AUTO_DELIVERY_TIMEOUT=${AUTO_DELIVERY_TIMEOUT:-30}
      - API_CARD_TIMEOUT=${API_CARD_TIMEOUT:-10}
      - BATCH_DATA_LOCK_TIMEOUT=${BATCH_DATA_LOCK_TIMEOUT:-5}
      # AI回复相关配置
      - AI_REPLY_ENABLED=${AI_REPLY_ENABLED:-false}
      - DEFAULT_AI_MODEL=${DEFAULT_AI_MODEL:-qwen-plus}
      - DEFAULT_AI_BASE_URL=${DEFAULT_AI_BASE_URL:-https://dashscope.aliyuncs.com/compatible-mode/v1}
      - AI_REQUEST_TIMEOUT=${AI_REQUEST_TIMEOUT:-30}
      - AI_MAX_TOKENS=${AI_MAX_TOKENS:-100}
      - WEBSOCKET_URL=${WEBSOCKET_URL:-wss://wss-goofish.dingtalk.com/}
      - HEARTBEAT_INTERVAL=${HEARTBEAT_INTERVAL:-15}
      - HEARTBEAT_TIMEOUT=${HEARTBEAT_TIMEOUT:-5}
      - TOKEN_REFRESH_INTERVAL=${TOKEN_REFRESH_INTERVAL:-3600}
      - TOKEN_RETRY_INTERVAL=${TOKEN_RETRY_INTERVAL:-300}
      - MESSAGE_EXPIRE_TIME=${MESSAGE_EXPIRE_TIME:-300000}

    networks:
      - xianyu-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    # 资源限制
    deploy:
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-512}M
          cpus: '${CPU_LIMIT:-0.5}'
        reservations:
          memory: ${MEMORY_RESERVATION:-256}M
          cpus: '${CPU_RESERVATION:-0.25}'

  # 可选：添加Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: xianyu-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    depends_on:
      - xianyu-app
    networks:
      - xianyu-network
    profiles:
      - with-nginx

networks:
  xianyu-network:
    driver: bridge

volumes:
  xianyu-data:
    driver: local
  xianyu-logs:
    driver: local
