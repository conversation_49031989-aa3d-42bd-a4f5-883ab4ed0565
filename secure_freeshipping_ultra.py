"""
自动免拼发货模块 - 超级混淆版本
代码经过多层编码和混淆处理
"""
import base64 as LsWYPXmT
import zlib as oxWwRTDp
import types as AUdcGvRk
import binascii as qKAaznVW


class pDLWZWoi:
    
    def __init__(self):
        self.vrCYrtTq = "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"
    
    def gdVvQitT(self):
        # 解密过程
        step1_var = self.vrCYrtTq[::-1]
        step2_var = bytes.fromhex(step1_var)
        step3_var = LsWYPXmT.b64decode(step2_var)
        step4_var = oxWwRTDp.decompress(step3_var)
        step5_var = step4_var.decode('utf-8')

        return step5_var
    
    def CHsKCWCD(self):
        """创建模块"""
        decoded_code = self.gdVvQitT()
        module_obj = AUdcGvRk.ModuleType('secure_freeshipping')
        exec(decoded_code, module_obj.__dict__)
        return module_obj


OhPXQtOT = pDLWZWoi()

amBCLCwC = OhPXQtOT.CHsKCWCD()

SecureFreeshipping = amBCLCwC.SecureFreeshipping

# 清理所有变量
del OhPXQtOT
del amBCLCwC
del pDLWZWoi
