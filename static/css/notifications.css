/* ================================
   【通知渠道菜单】卡片样式
   ================================ */
.channel-type-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
  min-height: 180px; /* 设置最小高度，让卡片更紧凑 */
}

.channel-type-card .card-body {
  padding: 1rem; /* 减少内边距 */
}

.channel-type-card .channel-icon {
  transition: transform 0.3s ease;
  margin-bottom: 0.75rem !important; /* 减少图标下方间距 */
}

.channel-type-card .channel-icon i {
  font-size: 2.2rem !important; /* 减小图标大小 */
}

.channel-type-card .card-title {
  font-size: 1.1rem; /* 减小标题字体 */
  margin-bottom: 0.5rem;
}

.channel-type-card .card-text {
  font-size: 0.85rem; /* 减小描述文字 */
  margin-bottom: 0.75rem;
}

.channel-type-card .btn {
  transition: all 0.3s ease;
  font-size: 0.85rem; /* 减小按钮文字 */
  padding: 0.375rem 0.75rem; /* 减小按钮内边距 */
}

.channel-type-card:hover {
  transform: translateY(-3px); /* 减少悬停位移 */
  box-shadow: 0 6px 20px rgba(0,0,0,0.12);
  border-color: var(--bs-primary);
}

.channel-type-card:hover .channel-icon {
  transform: scale(1.05); /* 减少悬停缩放 */
}

.channel-type-card:hover .btn {
  transform: scale(1.02); /* 减少按钮悬停缩放 */
}

/* 响应式调整 */
@media (max-width: 768px) {
  .channel-type-card {
    margin-bottom: 10px;
    min-height: 160px; /* 移动端稍微减小高度 */
  }

  .channel-type-card .card-body {
    padding: 0.75rem; /* 移动端减少内边距 */
  }

  .channel-type-card .channel-icon i {
    font-size: 2rem !important; /* 移动端图标更小 */
  }

  .channel-type-card .card-title {
    font-size: 1rem; /* 移动端标题更小 */
  }

  .channel-type-card .card-text {
    font-size: 0.8rem; /* 移动端描述文字更小 */
  }
}

/* 大屏幕优化 */
@media (min-width: 1400px) {
  .channel-type-card {
    min-height: 200px; /* 大屏幕稍微增加高度 */
  }
}
