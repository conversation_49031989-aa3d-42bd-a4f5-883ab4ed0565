/* ================================
   通用布局 - 侧边栏和导航
   ================================ */
.sidebar {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 250px;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  color: white;
  z-index: 1000;
  transition: all 0.3s ease;
  box-shadow: 2px 0 10px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 1.5rem 1rem;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  flex-shrink: 0;
}

.sidebar-brand {
  font-size: 1.25rem;
  font-weight: 700;
  color: white;
  text-decoration: none;
  display: flex;
  align-items: center;
}

.sidebar-nav {
  padding: 1rem 0;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

/* 侧边栏滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  transition: background 0.3s ease;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* Firefox滚动条样式 */
.sidebar-nav {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(255, 255, 255, 0.1);
}

.nav-item {
  margin: 0.25rem 0;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  color: rgba(255,255,255,0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
}

.nav-link:hover {
  color: white;
  background: rgba(255,255,255,0.1);
  border-left-color: rgba(255,255,255,0.5);
}

.nav-link.active {
  color: white;
  background: rgba(255,255,255,0.15);
  border-left-color: white;
}

.nav-link i {
  margin-right: 0.75rem;
  width: 20px;
  text-align: center;
}

.nav-divider {
  padding: 0.5rem 1rem;
  border-top: 1px solid rgba(255,255,255,0.1);
  margin-top: 0.5rem;
}

.nav-divider small {
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 主内容区域 */
.main-content {
  margin-left: 250px;
  min-height: 100vh;
  background: #f8fafc;
}

.content-header {
  background: white;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--border-color);
  box-shadow: var(--shadow-sm);
}

.content-body {
  padding: 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .mobile-toggle {
    display: block !important;
  }
}

.mobile-toggle {
  display: none;
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 1001;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.5rem;
  box-shadow: var(--shadow-md);
}

/* 内容区域样式 */
.content-section {
  display: none;
}

.content-section.active {
  display: block;
}
