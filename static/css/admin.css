/* ================================
   管理员功能样式 - 用户管理和数据管理
   ================================ */

/* 用户管理样式 */
.user-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid var(--border-color);
    border-radius: 12px;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.user-card .card-body {
    padding: 1.25rem;
}

.user-card .badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
}

/* 数据管理样式 */
.table-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid var(--border-color);
    border-radius: 8px;
}

.table th {
    position: sticky;
    top: 0;
    background: var(--dark-color);
    color: white;
    z-index: 10;
    border: none;
    font-weight: 600;
    font-size: 0.875rem;
    padding: 0.75rem;
}

.table td {
    padding: 0.75rem;
    font-size: 0.875rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: rgba(79, 70, 229, 0.05);
}

/* 数据统计卡片 */
.data-stats {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    border: none;
}

.data-stats .card-body {
    padding: 0.75rem;
}

/* 表格状态样式 */
#loadingTable,
#noTableSelected,
#noTableData {
    padding: 3rem 1rem;
}

#loadingTable .spinner-border {
    width: 3rem;
    height: 3rem;
    border-width: 0.3em;
}

#noTableSelected i,
#noTableData i {
    font-size: 4rem;
    color: var(--secondary-color);
    opacity: 0.5;
}

/* 按钮组样式 */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-left-radius: 6px;
    border-bottom-left-radius: 6px;
}

.btn-group .btn:last-child {
    border-top-right-radius: 6px;
    border-bottom-right-radius: 6px;
}

/* 表格选择器样式 */
#tableSelect {
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.875rem;
    transition: border-color 0.2s ease;
}

#tableSelect:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(79, 70, 229, 0.25);
}

/* 记录计数样式 */
#recordCount {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .user-card {
        margin-bottom: 1rem;
    }
    
    .table-container {
        max-height: 400px;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        font-size: 0.8rem;
    }
    
    .btn-group {
        flex-direction: column;
    }
    
    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.25rem;
    }
}

/* 加载状态动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-card,
.table-container {
    animation: fadeIn 0.3s ease-out;
}

/* 表格滚动条样式 */
.table-container::-webkit-scrollbar {
    width: 8px;
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb {
    background: var(--secondary-color);
    border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* 文本截断样式 */
.text-truncate-custom {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: inline-block;
}

/* 状态徽章样式 */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
}

/* 工具提示样式增强 */
[title] {
    cursor: help;
}

/* 空状态图标样式 */
.empty-state-icon {
    font-size: 4rem;
    color: var(--secondary-color);
    opacity: 0.3;
    margin-bottom: 1rem;
}

/* 操作按钮样式 */
.action-buttons .btn {
    margin: 0 0.125rem;
    padding: 0.375rem 0.75rem;
}

.action-buttons .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
}

/* 统计卡片渐变背景 */
.stats-card-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
}

.stats-card-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.stats-card-success {
    background: linear-gradient(135deg, var(--success-color) 0%, #0d9488 100%);
}

.stats-card-warning {
    background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
}
