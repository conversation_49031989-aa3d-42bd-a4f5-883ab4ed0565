/* 仪表盘样式 */
.dashboard-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

/* ================================
   【仪表盘菜单】相关样式
   ================================ */
.stat-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.stat-icon.primary {
  background: rgba(79, 70, 229, 0.1);
  color: var(--primary-color);
}

.stat-icon.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.stat-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.stat-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 0.5rem;
}

.stat-label {
  color: var(--secondary-color);
  font-size: 0.875rem;
  font-weight: 500;
}
