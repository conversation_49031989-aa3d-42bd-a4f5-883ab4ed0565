/* ================================
   【商品管理菜单】表格样式
   ================================ */
#itemsTableBody .btn-group {
  white-space: nowrap;
}

#itemsTableBody .btn-group .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
  border-radius: 0.25rem;
}

#itemsTableBody .btn-group .btn:first-child {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

#itemsTableBody .btn-group .btn:last-child {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left: 0;
}

#itemsTableBody .btn-group .btn i {
  font-size: 0.875rem;
}

/* 表格操作列样式 */
.table td:last-child {
  text-align: center;
  vertical-align: middle;
  padding: 0.5rem 0.25rem;
}

/* 表格文本截断优化 */
.table td {
  vertical-align: middle;
}

.table td[title] {
  cursor: help;
}

/* 商品表格特定优化 */
#itemsTableBody td:nth-child(4),
#itemsTableBody td:nth-child(5) {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
