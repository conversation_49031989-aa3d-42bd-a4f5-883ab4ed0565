/* ================================
   【账号管理菜单】相关样式
   ================================ */
.status-toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.status-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.status-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.status-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .status-slider {
  background-color: #10b981;
}

input:checked + .status-slider:before {
  transform: translateX(26px);
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  min-width: 2rem;
  height: 1.5rem;
}

.status-badge.enabled {
  background: #dcfce7;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.status-badge.disabled {
  background: #fef2f2;
  color: #991b1b;
  border: 1px solid #fecaca;
}

.account-row.disabled {
  opacity: 0.6;
  background-color: #f9fafb;
}

.account-row.disabled .cookie-value {
  background-color: #f3f4f6;
}

/* 关键词管理界面的状态提示 */
.account-badge .badge.bg-warning {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.disabled-account-notice {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 0.75rem;
  margin-bottom: 1rem;
  color: #92400e;
}

.disabled-account-notice .bi {
  color: #f59e0b;
}

/* 扫码登录按钮特殊样式 */
.qr-login-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.qr-login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
  background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
}

.qr-login-btn:active {
  transform: translateY(0);
}

.qr-login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.qr-login-btn:hover::before {
  left: 100%;
}

/* 二维码容器样式 */
.qr-code-wrapper {
  border: 3px solid #28a745;
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
  transition: all 0.3s ease;
}

.qr-code-wrapper:hover {
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

/* 步骤指引样式 */
.step-item {
  text-align: center;
}

.step-number {
  font-weight: bold;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

/* 手动输入按钮样式 */
.manual-input-btn {
  transition: all 0.3s ease;
}

.manual-input-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.2);
}

/* 等待提示样式 */
.bg-light-warning {
  background-color: #fff3cd !important;
}

.qr-loading-tip {
  animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
  0% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.8;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-toggle {
    width: 40px;
    height: 20px;
  }

  .status-slider:before {
    height: 14px;
    width: 14px;
    left: 3px;
    bottom: 3px;
  }

  input:checked + .status-slider:before {
    transform: translateX(20px);
  }

  .qr-login-btn, .manual-input-btn {
    margin-bottom: 10px;
    font-size: 0.9rem;
  }

  .step-item {
    margin-bottom: 15px;
  }

  .step-number {
    width: 25px !important;
    height: 25px !important;
    font-size: 12px;
  }
}
