/* ================================
   【日志管理菜单】相关样式
   ================================ */
.log-container {
  height: 70vh;
  overflow-y: auto;
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.2;
  padding: 8px;
  border-radius: 4px;
}

.log-entry {
  margin-bottom: 0px;
  padding: 1px 0;
  word-wrap: break-word;
  white-space: pre-wrap;
  line-height: 1.2;
}

.log-entry.DEBUG {
  color: #9cdcfe;
}

.log-entry.INFO {
  color: #4ec9b0;
}

.log-entry.WARNING {
  color: #dcdcaa;
}

.log-entry.ERROR {
  color: #f48771;
}

.log-entry.CRITICAL {
  color: #ff6b6b;
  font-weight: bold;
}

.log-timestamp {
  color: #808080;
}

.log-level {
  font-weight: bold;
  margin: 0 5px;
}

.log-source {
  color: #569cd6;
}

.log-message {
  color: inherit;
}

.log-container::-webkit-scrollbar {
  width: 8px;
}

.log-container::-webkit-scrollbar-track {
  background: #2d2d30;
}

.log-container::-webkit-scrollbar-thumb {
  background: #464647;
  border-radius: 4px;
}

.log-container::-webkit-scrollbar-thumb:hover {
  background: #5a5a5c;
}

/* 日志过滤标签样式 */
.filter-badge {
  cursor: pointer;
  transition: all 0.2s;
  margin-right: 0.5rem;
  margin-bottom: 0.25rem;
}

.filter-badge:hover {
  transform: scale(1.05);
}

.filter-badge.active {
  box-shadow: 0 0 0 2px rgba(255,255,255,0.5);
}

/* 自动刷新指示器 */
.auto-refresh-indicator {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* 日志行悬停效果 */
.log-entry:hover {
  background: rgba(255,255,255,0.05);
}

/* 商品搜索相关样式 */
.item-card {
  transition: transform 0.2s;
  border: none;
  border-radius: 15px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.item-card:hover {
  transform: translateY(-2px);
}

.item-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 10px;
}

.price {
  color: #e74c3c;
  font-weight: bold;
  font-size: 1.2em;
}

.seller-name {
  color: #6c757d;
  font-size: 0.9em;
}

.want-count {
  margin-top: 10px;
}

.want-count .badge {
  font-size: 0.8em;
}
