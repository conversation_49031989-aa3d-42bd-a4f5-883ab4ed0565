.qr-login-modal .ant-modal-body {
  padding: 24px;
}

.qr-login-content {
  text-align: center;
}

.steps-container {
  margin-bottom: 32px;
  padding: 0 24px;
}

.qr-code-container {
  margin: 24px 0;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

.qr-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.qr-code-display {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.qr-code-wrapper {
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  transition: all 0.3s ease;
}

.qr-code-wrapper:hover {
  border-color: #1890ff;
  background: #f0f8ff;
}

.qr-code-image {
  max-width: 280px;
  width: 100%;
  height: auto;
  display: block;
}

.qr-error {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.status-container {
  margin-top: 24px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #f0f0f0;
}

.status-container .anticon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .qr-login-modal {
    margin: 0;
    max-width: 100vw;
  }
  
  .qr-login-modal .ant-modal {
    margin: 0;
    padding: 0;
  }
  
  .steps-container {
    padding: 0 12px;
  }
  
  .qr-code-container {
    min-height: 300px;
    margin: 16px 0;
  }
  
  .qr-code-image {
    max-width: 240px;
  }
}

/* 动画效果 */
.qr-code-wrapper {
  animation: qrFadeIn 0.5s ease-in-out;
}

@keyframes qrFadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 状态图标动画 */
.status-container .anticon {
  transition: all 0.3s ease;
}

.status-container .anticon-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 步骤样式优化 */
.steps-container .ant-steps-item-title {
  font-size: 12px !important;
}

.steps-container .ant-steps-item-icon {
  width: 24px !important;
  height: 24px !important;
  line-height: 24px !important;
}
