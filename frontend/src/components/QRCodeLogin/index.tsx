import React, { useState, useEffect, useRef } from 'react';
import {
  Modal,
  Button,
  Space,
  Typography,
  Alert,
  Spin,
  Steps,
  App,
} from 'antd';
import {
  QrcodeOutlined,
  ReloadOutlined,
  PhoneOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
} from '@ant-design/icons';
import { accountApi } from '../../services/api';
import './QRCodeLogin.css';

const { Title, Text } = Typography;

interface QRCodeLoginProps {
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

interface QRCodeStatus {
  status: 'waiting' | 'scanned' | 'success' | 'failed' | 'expired' | 'already_processed';
  message?: string;
  account_info?: {
    account_id: string;
    is_new_account: boolean;
    real_cookie_refreshed: boolean;
    fallback_reason?: string;
    cookie_length?: number;
  };
}

const QRCodeLogin: React.FC<QRCodeLoginProps> = ({ visible, onClose, onSuccess }) => {
  const { message } = App.useApp();
  const [loading, setLoading] = useState(false);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [sessionId, setSessionId] = useState<string>('');
  const [status, setStatus] = useState<QRCodeStatus['status']>('waiting');
  const [statusMessage, setStatusMessage] = useState<string>('等待扫码...');
  const [showVerification, setShowVerification] = useState(false);
  const checkIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // 生成二维码
  const generateQRCode = async () => {
    try {
      setLoading(true);
      setStatus('waiting');
      setStatusMessage('正在生成二维码...');
      setShowVerification(false);

      const response = await accountApi.generateQRCode();

      console.log('QR Code API Response:', response);

      // 后端直接返回数据，不需要检查 response.data
      const data = response.data || response;

      console.log('Parsed data:', data);

      if (data.success) {
        console.log('Setting QR code URL:', data.qr_code_url);
        console.log('Setting session ID:', data.session_id);
        setQrCodeUrl(data.qr_code_url);
        setSessionId(data.session_id);
        setStatusMessage('等待扫码...');
        startStatusCheck(data.session_id);
      } else {
        throw new Error(data.message || '生成二维码失败');
      }
    } catch (error: any) {
      console.error('生成二维码失败:', error);
      message.error(error.message || '生成二维码失败');
      setStatusMessage('生成二维码失败');
    } finally {
      setLoading(false);
    }
  };

  // 开始状态检查
  const startStatusCheck = (sessionId: string) => {
    if (checkIntervalRef.current) {
      clearInterval(checkIntervalRef.current);
    }

    checkIntervalRef.current = setInterval(() => {
      checkQRCodeStatus(sessionId);
    }, 2000);
  };

  // 检查二维码状态
  const checkQRCodeStatus = async (sessionId: string) => {
    try {
      const response = await accountApi.checkQRCodeStatus(sessionId);

      // 后端直接返回数据，不需要检查 response.data
      const data = response.data || response;

      if (data.success) {
        const statusData: QRCodeStatus = data;
        setStatus(statusData.status);

        switch (statusData.status) {
          case 'waiting':
            setStatusMessage('等待扫码...');
            break;
          case 'scanned':
            setStatusMessage('已扫码，请在手机上确认...');
            break;
          case 'success':
            setStatusMessage('登录成功！');
            handleSuccess(statusData);
            break;
          case 'failed':
            setStatusMessage(statusData.message || '登录失败');
            if (statusData.message?.includes('需要手机验证')) {
              setShowVerification(true);
            }
            stopStatusCheck();
            break;
          case 'expired':
            setStatusMessage('二维码已过期，请重新生成');
            stopStatusCheck();
            break;
          case 'already_processed':
            setStatusMessage('登录已完成');
            stopStatusCheck();
            break;
        }
      }
    } catch (error) {
      console.error('检查二维码状态失败:', error);
    }
  };

  // 停止状态检查
  const stopStatusCheck = () => {
    if (checkIntervalRef.current) {
      clearInterval(checkIntervalRef.current);
      checkIntervalRef.current = null;
    }
  };

  // 处理登录成功
  const handleSuccess = (statusData: QRCodeStatus) => {
    stopStatusCheck();
    
    if (statusData.account_info) {
      const { account_id, is_new_account, real_cookie_refreshed, fallback_reason } = statusData.account_info;
      
      let successMessage = `账号 "${account_id}" ${is_new_account ? '添加' : '更新'}成功！`;
      
      if (real_cookie_refreshed === true) {
        successMessage += '\n✅ 真实Cookie获取并保存成功';
        message.success(successMessage);
      } else if (real_cookie_refreshed === false) {
        successMessage += '\n⚠️ 真实Cookie获取失败，已保存原始扫码Cookie';
        if (fallback_reason) {
          successMessage += `\n原因: ${fallback_reason}`;
        }
        message.warning(successMessage);
      } else {
        message.success(successMessage);
      }
    }

    // 延迟关闭模态框
    setTimeout(() => {
      onClose();
      onSuccess?.();
    }, 3000);
  };

  // 刷新二维码
  const refreshQRCode = () => {
    generateQRCode();
  };

  // 模态框显示时生成二维码
  useEffect(() => {
    if (visible) {
      generateQRCode();
    } else {
      stopStatusCheck();
      setQrCodeUrl('');
      setSessionId('');
      setStatus('waiting');
      setStatusMessage('等待扫码...');
      setShowVerification(false);
    }

    return () => {
      stopStatusCheck();
    };
  }, [visible]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      stopStatusCheck();
    };
  }, []);

  const getStatusIcon = () => {
    switch (status) {
      case 'waiting':
        return <QrcodeOutlined style={{ color: '#1890ff' }} />;
      case 'scanned':
        return <PhoneOutlined style={{ color: '#faad14' }} />;
      case 'success':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'failed':
      case 'expired':
        return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
      default:
        return <QrcodeOutlined style={{ color: '#1890ff' }} />;
    }
  };

  return (
    <Modal
      title={
        <Space>
          <QrcodeOutlined />
          <span>扫码登录闲鱼账号</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={600}
      footer={[
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
        <Button
          key="refresh"
          type="primary"
          icon={<ReloadOutlined />}
          onClick={refreshQRCode}
          loading={loading}
        >
          重新生成二维码
        </Button>,
      ]}
      className="qr-login-modal"
    >
      <div className="qr-login-content">
        {/* 步骤指引 */}
        <div className="steps-container">
          <Steps
            size="small"
            current={status === 'waiting' ? 0 : status === 'scanned' ? 1 : 2}
            items={[
              {
                title: '打开闲鱼APP',
                icon: <PhoneOutlined />,
              },
              {
                title: '扫描二维码',
                icon: <QrcodeOutlined />,
              },
              {
                title: '自动添加账号',
                icon: <CheckCircleOutlined />,
              },
            ]}
          />
        </div>

        {/* 二维码区域 */}
        <div className="qr-code-container">
          {loading ? (
            <div className="qr-loading">
              <Spin size="large" />
              <Text style={{ marginTop: 16, display: 'block' }}>正在生成二维码...</Text>
              <Alert
                message="二维码生成较慢，请耐心等待"
                type="warning"
                showIcon
                style={{ marginTop: 16, maxWidth: 300 }}
              />
            </div>
          ) : qrCodeUrl ? (
            <div className="qr-code-display">
              <div className="qr-code-wrapper">
                <img src={qrCodeUrl} alt="登录二维码" className="qr-code-image" />
              </div>
              <Title level={5} style={{ color: '#52c41a', marginTop: 16 }}>
                <PhoneOutlined style={{ marginRight: 8 }} />
                请使用闲鱼APP扫描二维码
              </Title>
              <Alert
                message="扫码后请等待页面提示，系统会自动获取并保存您的账号信息"
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            </div>
          ) : (
            <div className="qr-error">
              <Text type="danger">生成二维码失败，请重试</Text>
            </div>
          )}
        </div>

        {/* 状态显示 */}
        <div className="status-container">
          <Space>
            {(status === 'waiting' || status === 'scanned') && <Spin size="small" />}
            {getStatusIcon()}
            <Text>{statusMessage}</Text>
          </Space>
        </div>

        {/* 验证提示 */}
        {showVerification && (
          <Alert
            message="账号需要手机验证"
            description={
              <div>
                <Text>请按照以下步骤完成验证：</Text>
                <ol style={{ marginTop: 8, marginBottom: 0 }}>
                  <li>打开验证页面完成手机验证</li>
                  <li>验证完成后，重新扫码登录</li>
                </ol>
              </div>
            }
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </div>
    </Modal>
  );
};

export default QRCodeLogin;
