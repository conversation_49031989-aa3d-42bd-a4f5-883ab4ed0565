import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  Layout,
  Menu,
  Button,
  Drawer,
  Typography,
  Divider,
  Badge,
  Space,
  message,
} from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MessageOutlined,
  LogoutOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { menuItems, adminMenuItems, otherMenuItems } from '../../config/menu';
import type { MenuItem } from '../../types';
import './MainLayout.css';

const { Header, Sider, Content } = Layout;
const { Text } = Typography;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [isAdmin, setIsAdmin] = useState(false);
  const [systemVersion, setSystemVersion] = useState('加载中...');
  const navigate = useNavigate();
  const location = useLocation();

  // 登出功能
  const handleLogout = () => {
    localStorage.removeItem('auth_token');
    localStorage.removeItem('user_info');
    message.success('已退出登录');
    navigate('/login');
  };

  // 检查是否为移动端
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 检查用户权限
  useEffect(() => {
    const userRole = localStorage.getItem('user_role');
    const userInfo = localStorage.getItem('user_info');
    console.log('用户角色检查:', { userRole, userInfo, isAdmin: userRole === 'admin' });
    setIsAdmin(userRole === 'admin');
  }, []);

  // 获取系统版本
  useEffect(() => {
    // TODO: 从API获取系统版本
    setSystemVersion('v1.0.0');
  }, []);

  // 处理菜单点击
  const handleMenuClick = ({ key }: { key: string }) => {
    if (key === 'logout') {
      handleLogout();
      return;
    }

    navigate(`/${key}`);
    
    // 移动端关闭抽屉
    if (isMobile) {
      setMobileDrawerVisible(false);
    }
  };



  // 获取当前选中的菜单项
  const getCurrentMenuKey = () => {
    const path = location.pathname;
    return path.substring(1) || 'dashboard';
  };

  // 构建菜单项
  const buildMenuItems = (items: MenuItem[]): any[] => {
    return items.map(item => ({
      key: item.key,
      icon: item.icon,
      label: item.label,
      children: item.children ? buildMenuItems(item.children) : undefined,
    }));
  };

  // 侧边栏内容
  const siderContent = (
    <div className="sidebar-content">
      {/* 侧边栏头部 */}
      <div className="sidebar-header">
        <div className="sidebar-brand">
          <MessageOutlined className="brand-icon" />
          {!collapsed && <span className="brand-text">闲鱼管理系统</span>}
        </div>
      </div>

      {/* 菜单区域 */}
      <div className="sidebar-menu-area">
        {/* 主要菜单 */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[getCurrentMenuKey()]}
          items={buildMenuItems(menuItems)}
          onClick={handleMenuClick}
          className="main-menu"
        />

        {/* 管理员菜单 */}
        {isAdmin && (
          <>
            <Divider className="menu-divider">
              <Text type="secondary" className="divider-text">管理员功能</Text>
            </Divider>
            <Menu
              theme="dark"
              mode="inline"
              selectedKeys={[getCurrentMenuKey()]}
              items={buildMenuItems(adminMenuItems)}
              onClick={handleMenuClick}
              className="admin-menu"
            />
          </>
        )}


        {/* 其他菜单 */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[getCurrentMenuKey()]}
          items={buildMenuItems(otherMenuItems)}
          onClick={handleMenuClick}
          className="other-menu"
        />
      </div>

      {/* 底部区域 */}
      <div className="sidebar-footer">
        {/* 系统操作菜单 */}
        <Divider className="menu-divider">
          <Text type="secondary" className="divider-text">系统操作</Text>
        </Divider>

        {/* 登出按钮 */}
        <div className="logout-section">
          <Button
            type="text"
            icon={<LogoutOutlined />}
            onClick={handleLogout}
            className="logout-btn"
            block
          >
            {!collapsed && <span style={{ color: 'rgba(255, 255, 255, 0.85)' }}>退出登录</span>}
          </Button>
        </div>

        {/* 版本信息 */}
        <div className="version-info">
          {!collapsed ? (
            <div className="version-full">
              <div className="version-badge">
                <Badge color="blue" text={`版本: ${systemVersion}`} />
              </div>
              <div className="copyright">
                <Text type="secondary" style={{ fontSize: '11px', color: 'rgba(255, 255, 255, 0.45)' }}>
                  © 2024 闲鱼管理系统
                </Text>
              </div>
            </div>
          ) : (
            <div className="version-collapsed">
              <Badge color="blue" />
            </div>
          )}
        </div>
      </div>
    </div>
  );

  return (
    <Layout className="main-layout">
      {/* 桌面端侧边栏 */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          className="layout-sider"
          width={250}
          collapsedWidth={80}
        >
          {siderContent}
        </Sider>
      )}

      {/* 移动端抽屉 */}
      {isMobile && (
        <Drawer
          title={
            <Space>
              <MessageOutlined />
              <span>闲鱼管理系统</span>
            </Space>
          }
          placement="left"
          onClose={() => setMobileDrawerVisible(false)}
          open={mobileDrawerVisible}
          styles={{
            body: { padding: 0, backgroundColor: '#001529' },
            header: { backgroundColor: '#001529', borderBottom: '1px solid #303030' }
          }}
          width={250}
        >
          {siderContent}
        </Drawer>
      )}

      <Layout className={`layout-content ${!isMobile && collapsed ? 'collapsed' : ''}`}>
        {/* 头部 */}
        <Header className="layout-header">
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
            <Button
              type="text"
              icon={
                isMobile ? (
                  <MenuUnfoldOutlined />
                ) : collapsed ? (
                  <MenuUnfoldOutlined />
                ) : (
                  <MenuFoldOutlined />
                )
              }
              onClick={() => {
                if (isMobile) {
                  setMobileDrawerVisible(true);
                } else {
                  setCollapsed(!collapsed);
                }
              }}
              className="trigger-btn"
            />

            <Space>
              <Space>
                <UserOutlined />
                <Text>管理员</Text>
              </Space>
              <Button
                type="text"
                icon={<LogoutOutlined />}
                onClick={handleLogout}
                title="退出登录"
              >
                退出
              </Button>
            </Space>
          </div>
        </Header>

        {/* 主内容区域 */}
        <Content className="layout-main-content">
          <div className="content-wrapper">
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
