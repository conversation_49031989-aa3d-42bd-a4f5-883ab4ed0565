import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Input,
  Select,
  Space,
  Tag,
  Popconfirm,
  message,
  Row,
  Col,
  Tooltip,
  Spin,
} from 'antd';
import {
  FileTextOutlined,
  SearchOutlined,
  ReloadOutlined,
  DeleteOutlined,
  ClearOutlined,
} from '@ant-design/icons';
import { orderApi, accountApi } from '../../services/api';
import type { Order, Account, PaginationParams, SearchParams } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

const OrderManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [pagination, setPagination] = useState<PaginationParams>({
    page: 1,
    pageSize: 20,
    total: 0,
  });

  // 加载账号列表
  const loadAccounts = async () => {
    try {
      const response = await accountApi.getAccounts();
      if (response.success) {
        setAccounts(response.data || []);
      }
    } catch (error) {
      console.error('加载账号列表失败:', error);
    }
  };

  // 加载订单列表
  const loadOrders = async (params?: SearchParams & PaginationParams) => {
    try {
      setLoading(true);
      const searchParams = {
        keyword: searchKeyword,
        cookieId: selectedAccount,
        status: selectedStatus,
        page: pagination.page,
        pageSize: pagination.pageSize,
        ...params,
      };

      const response = await orderApi.getOrders(searchParams);
      if (response.success) {
        setOrders(response.data?.orders || []);
        setPagination(prev => ({
          ...prev,
          total: response.data?.total || 0,
        }));
      }
    } catch (error: any) {
      console.error('加载订单列表失败:', error);
      message.error('加载订单列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新订单状态
  const handleUpdateOrderStatus = async (id: string, status: Order['status']) => {
    try {
      const response = await orderApi.updateOrderStatus(id, status);
      if (response.success) {
        message.success('订单状态更新成功');
        loadOrders();
      }
    } catch (error: any) {
      message.error(error.message || '更新订单状态失败');
    }
  };

  // 删除订单
  const handleDeleteOrder = async (id: string) => {
    try {
      const response = await orderApi.deleteOrder(id);
      if (response.success) {
        message.success('订单删除成功');
        loadOrders();
      }
    } catch (error: any) {
      message.error(error.message || '删除订单失败');
    }
  };

  // 批量删除订单
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的订单');
      return;
    }

    try {
      const response = await orderApi.batchDeleteOrders(selectedRowKeys);
      if (response.success) {
        message.success(`成功删除 ${selectedRowKeys.length} 个订单`);
        setSelectedRowKeys([]);
        loadOrders();
      }
    } catch (error: any) {
      message.error(error.message || '批量删除失败');
    }
  };

  // 搜索订单
  const handleSearch = () => {
    setPagination(prev => ({ ...prev, page: 1 }));
    loadOrders({ page: 1 });
  };

  // 清空筛选
  const handleClearFilters = () => {
    setSearchKeyword('');
    setSelectedAccount('');
    setSelectedStatus('');
    setPagination(prev => ({ ...prev, page: 1 }));
    loadOrders({ keyword: '', cookieId: '', status: '', page: 1 });
  };

  useEffect(() => {
    loadAccounts();
    loadOrders();
  }, []);

  // 订单状态配置
  const statusConfig = {
    processing: { color: 'processing', text: '处理中' },
    processed: { color: 'success', text: '已处理' },
    completed: { color: 'default', text: '已完成' },
    unknown: { color: 'warning', text: '未知' },
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <FileTextOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              订单管理
            </Title>
            <Text type="secondary">查看和管理所有订单信息</Text>
          </div>
          <div className="header-extra">
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => loadOrders()}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 筛选区域 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <div>
              <Text strong>筛选账号</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="所有账号"
                allowClear
                value={selectedAccount || undefined}
                onChange={(value) => setSelectedAccount(value || '')}
              >
                {accounts.map(account => (
                  <Option key={account.id} value={account.id}>
                    {account.id}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={8} md={6}>
            <div>
              <Text strong>订单状态</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="所有状态"
                allowClear
                value={selectedStatus || undefined}
                onChange={(value) => setSelectedStatus(value || '')}
              >
                <Option value="processing">处理中</Option>
                <Option value="processed">已处理</Option>
                <Option value="completed">已完成</Option>
                <Option value="unknown">未知</Option>
              </Select>
            </div>
          </Col>

          <Col xs={24} sm={8} md={12}>
            <div>
              <Text strong>搜索</Text>
              <div style={{ marginTop: 4 }}>
                <Input.Group compact>
                  <Input
                    style={{ width: 'calc(100% - 80px)' }}
                    placeholder="搜索订单ID或商品ID..."
                    value={searchKeyword}
                    onChange={(e) => setSearchKeyword(e.target.value)}
                    onPressEnter={handleSearch}
                  />
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                  />
                </Input.Group>
              </div>
            </div>
          </Col>
        </Row>

        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Button
            icon={<ClearOutlined />}
            onClick={handleClearFilters}
          >
            清空筛选
          </Button>
        </div>
      </Card>

      {/* 订单列表 */}
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <FileTextOutlined />
              <span>订单列表</span>
            </Space>
            <Popconfirm
              title="确定要删除选中的订单吗？"
              description={`将删除 ${selectedRowKeys.length} 个订单，此操作不可恢复。`}
              onConfirm={handleBatchDelete}
              disabled={selectedRowKeys.length === 0}
              okText="确定"
              cancelText="取消"
            >
              <Button
                danger
                icon={<DeleteOutlined />}
                disabled={selectedRowKeys.length === 0}
              >
                批量删除 ({selectedRowKeys.length})
              </Button>
            </Popconfirm>
          </div>
        }
      >
        <Table
          rowSelection={{
            selectedRowKeys,
            onChange: setSelectedRowKeys,
            preserveSelectedRowKeys: true,
          }}
          columns={[
            {
              title: '订单ID',
              dataIndex: 'order_id',
              key: 'order_id',
              width: '15%',
              render: (text: string) => (
                <Text code copyable={{ text }}>
                  {text}
                </Text>
              ),
            },
            {
              title: '商品ID',
              dataIndex: 'item_id',
              key: 'item_id',
              width: '12%',
              render: (text: string) => (
                <Text code copyable={{ text }}>
                  {text}
                </Text>
              ),
            },
            {
              title: '买家ID',
              dataIndex: 'buyer_id',
              key: 'buyer_id',
              width: '10%',
              render: (text: string) => (
                <Text code>{text}</Text>
              ),
            },
            {
              title: '规格信息',
              dataIndex: 'spec_info',
              key: 'spec_info',
              width: '12%',
              render: (text: string) => (
                <Tooltip title={text}>
                  <Text ellipsis style={{ maxWidth: 120 }}>
                    {text || '-'}
                  </Text>
                </Tooltip>
              ),
            },
            {
              title: '数量',
              dataIndex: 'quantity',
              key: 'quantity',
              width: '8%',
              render: (quantity: number) => (
                <Tag color="blue">{quantity}</Tag>
              ),
            },
            {
              title: '金额',
              dataIndex: 'amount',
              key: 'amount',
              width: '10%',
              render: (amount: string) => (
                <Text strong style={{ color: '#f50' }}>
                  ¥{amount}
                </Text>
              ),
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              width: '8%',
              render: (status: Order['status']) => {
                const config = statusConfig[status] || statusConfig.unknown;
                return (
                  <Tag color={config.color}>
                    {config.text}
                  </Tag>
                );
              },
            },
            {
              title: '账号ID',
              dataIndex: 'cookie_id',
              key: 'cookie_id',
              width: '10%',
              render: (text: string) => (
                <Text code>{text}</Text>
              ),
            },
            {
              title: '创建时间',
              dataIndex: 'created_at',
              key: 'created_at',
              width: '12%',
              render: (time: string) => (
                <Text type="secondary">
                  {dayjs(time).format('MM-DD HH:mm')}
                </Text>
              ),
            },
            {
              title: '操作',
              key: 'action',
              width: '13%',
              render: (_, record: Order) => (
                <Space size="small">
                  <Select
                    size="small"
                    value={record.status}
                    onChange={(status) => handleUpdateOrderStatus(record.id, status)}
                    style={{ width: 80 }}
                  >
                    <Option value="processing">处理中</Option>
                    <Option value="processed">已处理</Option>
                    <Option value="completed">已完成</Option>
                    <Option value="unknown">未知</Option>
                  </Select>
                  <Popconfirm
                    title="确定要删除这个订单吗？"
                    onConfirm={() => handleDeleteOrder(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                    />
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            current: pagination.page,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            onChange: (page, pageSize) => {
              const newPagination = { page, pageSize, total: pagination.total };
              setPagination(newPagination);
              loadOrders(newPagination);
            },
          }}
          scroll={{ x: 1200 }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', textAlign: 'center' }}>
                <FileTextOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <div>
                  <Title level={4} type="secondary">暂无订单</Title>
                  <Text type="secondary">暂时没有订单数据</Text>
                </div>
              </div>
            ),
          }}
        />
      </Card>
    </div>
  );
};

export default OrderManagement;
