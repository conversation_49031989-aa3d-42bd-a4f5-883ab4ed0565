import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  Form,
  message,
  Row,
  Col,
  Alert,
  Popconfirm,
} from 'antd';
import {
  CommentOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  ShoppingOutlined,
  MessageOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import { keywordApi, accountApi, itemApi } from '../../services/api';
import type { Keyword, Account, Item } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ItemReply: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [keywords, setKeywords] = useState<Keyword[]>([]);
  const [items, setItems] = useState<Item[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingKeyword, setEditingKeyword] = useState<Keyword | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');

  const [form] = Form.useForm();

  // 加载账号列表
  const loadAccounts = async () => {
    try {
      const response = await accountApi.getAccounts();
      if (response.success) {
        setAccounts(response.data || []);
      }
    } catch (error) {
      console.error('加载账号列表失败:', error);
    }
  };

  // 加载指定商品的关键词
  const loadItemKeywords = async (accountId: string) => {
    if (!accountId) return;

    try {
      setLoading(true);
      const response = await keywordApi.getKeywords(accountId);
      if (response.success) {
        // 只显示有商品ID的关键词
        const itemKeywords = (response.data || []).filter(keyword => keyword.item_id);
        setKeywords(itemKeywords);
      }
    } catch (error: any) {
      console.error('加载关键词失败:', error);
      message.error('加载关键词失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载商品列表
  const loadItems = async (accountId: string) => {
    if (!accountId) return;

    try {
      const response = await itemApi.getItemsByAccount(accountId);
      if (response.success) {
        setItems(response.data?.items || []);
      }
    } catch (error) {
      console.error('加载商品列表失败:', error);
    }
  };

  // 账号选择变化
  const handleAccountChange = (accountId: string) => {
    setSelectedAccount(accountId);
    if (accountId) {
      loadItemKeywords(accountId);
      loadItems(accountId);
    } else {
      setKeywords([]);
      setItems([]);
    }
  };

  // 添加关键词
  const handleAddKeyword = async (values: any) => {
    try {
      const keywordData = {
        cookie_id: selectedAccount,
        keyword: values.keyword,
        reply: values.reply || '',
        item_id: values.item_id,
        type: 'text' as const,
      };

      const response = await keywordApi.addKeyword(keywordData);
      if (response.success) {
        message.success('商品关键词添加成功');
        setModalVisible(false);
        form.resetFields();
        loadItemKeywords(selectedAccount);
      }
    } catch (error: any) {
      message.error(error.message || '添加关键词失败');
    }
  };

  // 编辑关键词
  const handleEditKeyword = async (values: any) => {
    if (!editingKeyword) return;

    try {
      const response = await keywordApi.updateKeyword(editingKeyword.id, values);
      if (response.success) {
        message.success('商品关键词更新成功');
        setModalVisible(false);
        setEditingKeyword(null);
        form.resetFields();
        loadItemKeywords(selectedAccount);
      }
    } catch (error: any) {
      message.error(error.message || '更新关键词失败');
    }
  };

  // 删除关键词
  const handleDeleteKeyword = async (id: string) => {
    try {
      const response = await keywordApi.deleteKeyword(id);
      if (response.success) {
        message.success('商品关键词删除成功');
        loadItemKeywords(selectedAccount);
      }
    } catch (error: any) {
      message.error(error.message || '删除关键词失败');
    }
  };

  // 打开编辑模态框
  const openEditModal = (keyword: Keyword) => {
    setEditingKeyword(keyword);
    form.setFieldsValue({
      keyword: keyword.keyword,
      reply: keyword.reply,
      item_id: keyword.item_id,
    });
    setModalVisible(true);
  };

  useEffect(() => {
    loadAccounts();
  }, []);

  // 获取商品信息
  const getItemInfo = (itemId: string) => {
    return items.find(item => item.item_id === itemId);
  };

  // 过滤关键词
  const filteredKeywords = keywords.filter(keyword =>
    !searchKeyword ||
    keyword.keyword.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    keyword.reply.toLowerCase().includes(searchKeyword.toLowerCase()) ||
    getItemInfo(keyword.item_id)?.title.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <CommentOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              指定商品回复
            </Title>
            <Text type="secondary">为特定商品设置专属的关键词自动回复</Text>
          </div>
        </div>
      </div>

      {/* 账号选择器 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} md={18}>
            <div>
              <Text strong>选择账号</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="🔍 请选择一个账号开始配置..."
                value={selectedAccount || undefined}
                onChange={handleAccountChange}
                size="large"
              >
                {accounts.map(account => (
                  <Option key={account.id} value={account.id}>
                    <Space>
                      <span>{account.id}</span>
                      <Tag color={account.status ? 'success' : 'default'} size="small">
                        {account.status ? '启用' : '禁用'}
                      </Tag>
                    </Space>
                  </Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col xs={24} md={6}>
            <div>
              <Text strong>&nbsp;</Text>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={loadAccounts}
                style={{ width: '100%', marginTop: 4 }}
                size="large"
              >
                刷新列表
              </Button>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 商品关键词管理 */}
      {selectedAccount && (
        <Card
          title={
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
              <Space>
                <ShoppingOutlined />
                <span>商品关键词列表</span>
                <Tag color="blue">{filteredKeywords.length} 个关键词</Tag>
              </Space>
              <Space>
                <Input
                  placeholder="搜索关键词、回复内容或商品..."
                  value={searchKeyword}
                  onChange={(e) => setSearchKeyword(e.target.value)}
                  style={{ width: 300 }}
                  prefix={<SearchOutlined />}
                  allowClear
                />
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => setModalVisible(true)}
                >
                  添加商品关键词
                </Button>
              </Space>
            </div>
          }
        >
          <Alert
            message="商品关键词说明"
            description="商品关键词仅在对应商品的聊天中生效，优先级高于通用关键词。当用户在特定商品页面咨询时，系统会优先匹配该商品的专属关键词。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Table
            columns={[
              {
                title: '关键词',
                dataIndex: 'keyword',
                key: 'keyword',
                width: '15%',
                render: (text: string) => (
                  <Tag color="blue">{text}</Tag>
                ),
              },
              {
                title: '回复内容',
                dataIndex: 'reply',
                key: 'reply',
                width: '30%',
                render: (text: string) => (
                  text ? (
                    <Text ellipsis={{ tooltip: text }}>
                      {text}
                    </Text>
                  ) : (
                    <Text type="secondary" italic>
                      不回复（屏蔽消息）
                    </Text>
                  )
                ),
              },
              {
                title: '关联商品',
                dataIndex: 'item_id',
                key: 'item_id',
                width: '25%',
                render: (itemId: string) => {
                  const item = getItemInfo(itemId);
                  return item ? (
                    <div>
                      <Text strong ellipsis={{ tooltip: item.title }}>
                        {item.title}
                      </Text>
                      <br />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        ID: {itemId}
                      </Text>
                    </div>
                  ) : (
                    <Text type="secondary">商品不存在</Text>
                  );
                },
              },
              {
                title: '商品价格',
                dataIndex: 'item_id',
                key: 'price',
                width: '10%',
                render: (itemId: string) => {
                  const item = getItemInfo(itemId);
                  return item ? (
                    <Text strong style={{ color: '#f50' }}>
                      ¥{item.price}
                    </Text>
                  ) : '-';
                },
              },
              {
                title: '创建时间',
                dataIndex: 'created_at',
                key: 'created_at',
                width: '12%',
                render: (time: string) => (
                  <Text type="secondary">
                    {dayjs(time).format('MM-DD HH:mm')}
                  </Text>
                ),
              },
              {
                title: '操作',
                key: 'action',
                width: '8%',
                render: (_, record: Keyword) => (
                  <Space size="small">
                    <Button
                      type="text"
                      size="small"
                      icon={<EditOutlined />}
                      onClick={() => openEditModal(record)}
                    />
                    <Popconfirm
                      title="确定要删除这个商品关键词吗？"
                      onConfirm={() => handleDeleteKeyword(record.id)}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        type="text"
                        danger
                        size="small"
                        icon={<DeleteOutlined />}
                      />
                    </Popconfirm>
                  </Space>
                ),
              },
            ]}
            dataSource={filteredKeywords}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            }}
            locale={{
              emptyText: (
                <div style={{ padding: '40px 0', textAlign: 'center' }}>
                  <ShoppingOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                  <div>
                    <Title level={4} type="secondary">暂无商品关键词</Title>
                    <Text type="secondary">请添加商品专属的关键词回复</Text>
                  </div>
                </div>
              ),
            }}
          />
        </Card>
      )}

      {/* 添加/编辑商品关键词模态框 */}
      <Modal
        title={
          <Space>
            {editingKeyword ? <EditOutlined /> : <PlusOutlined />}
            <span>{editingKeyword ? '编辑商品关键词' : '添加商品关键词'}</span>
          </Space>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingKeyword(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Alert
          message="商品关键词优势"
          description="商品关键词仅在对应商品的聊天中生效，可以为不同商品设置不同的回复策略，提供更精准的客服体验。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={editingKeyword ? handleEditKeyword : handleAddKeyword}
        >
          <Form.Item
            label="关键词"
            name="keyword"
            rules={[{ required: true, message: '请输入关键词' }]}
          >
            <Input placeholder="例如：尺寸" />
          </Form.Item>

          <Form.Item
            label="自动回复内容"
            name="reply"
            help="留空表示不回复，可用于屏蔽特定消息"
          >
            <TextArea
              rows={3}
              placeholder="例如：此商品有S、M、L三个尺寸可选，请根据您的需要选择合适的尺寸"
            />
          </Form.Item>

          <Form.Item
            label="关联商品"
            name="item_id"
            rules={[{ required: true, message: '请选择关联商品' }]}
            help="选择此关键词生效的商品"
          >
            <Select
              placeholder="选择商品"
              showSearch
              filterOption={(input, option) =>
                (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
              }
            >
              {items.map(item => (
                <Option key={item.item_id} value={item.item_id}>
                  <div>
                    <Text ellipsis style={{ maxWidth: 300 }}>
                      {item.title}
                    </Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      ID: {item.item_id} | 价格: ¥{item.price}
                    </Text>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingKeyword ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ItemReply;
