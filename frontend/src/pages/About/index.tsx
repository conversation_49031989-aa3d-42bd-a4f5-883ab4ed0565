import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Row,
  Col,
  Space,
  Divider,
  Tag,
  Button,
  Descriptions,
  Alert,
  Statistic
} from 'antd';
import {
  InfoCircleOutlined,
  GithubOutlined,
  BugOutlined,
  HeartOutlined,
  RocketOutlined,
  TeamOutlined,
  ClockCircleOutlined,
  DatabaseOutlined,
  ApiOutlined,
  SafetyOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;

const About: React.FC = () => {
  const [systemInfo, setSystemInfo] = useState({
    version: 'v1.0.0',
    buildTime: '2024-01-15 10:30:00',
    uptime: '7天 12小时 30分钟',
    totalUsers: 156,
    totalMessages: 12580,
    totalKeywords: 89
  });

  useEffect(() => {
    loadSystemInfo();
  }, []);

  const loadSystemInfo = async () => {
    try {
      // 这里应该调用API获取系统信息
      // const info = await systemApi.getSystemInfo();
      // setSystemInfo(info);
    } catch (error) {
      console.error('加载系统信息失败:', error);
    }
  };

  const features = [
    {
      icon: <ApiOutlined style={{ color: '#1890ff' }} />,
      title: '智能回复',
      description: '基于关键词匹配的自动回复系统，支持多种回复策略'
    },
    {
      icon: <DatabaseOutlined style={{ color: '#52c41a' }} />,
      title: '数据管理',
      description: '完整的商品信息管理，支持批量导入导出'
    },
    {
      icon: <SafetyOutlined style={{ color: '#faad14' }} />,
      title: '安全可靠',
      description: '多层安全防护，保障用户数据安全'
    },
    {
      icon: <TeamOutlined style={{ color: '#722ed1' }} />,
      title: '多用户支持',
      description: '支持多用户管理，权限分级控制'
    }
  ];

  const techStack = [
    { name: 'React', version: '18.x', color: '#61dafb' },
    { name: 'TypeScript', version: '5.x', color: '#3178c6' },
    { name: 'Ant Design', version: '5.x', color: '#1890ff' },
    { name: 'Vite', version: '5.x', color: '#646cff' },
    { name: 'Python', version: '3.x', color: '#3776ab' },
    { name: 'FastAPI', version: '0.x', color: '#009688' }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <Title level={2}>
          <InfoCircleOutlined style={{ marginRight: '8px' }} />
          关于系统
        </Title>
        <Text type="secondary">闲鱼自动回复系统 - 让交易更简单</Text>
      </div>

      <Row gutter={[24, 24]}>
        {/* 系统信息 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <RocketOutlined />
                系统信息
              </Space>
            }
          >
            <Descriptions column={1} size="small">
              <Descriptions.Item label="系统版本">
                <Tag color="blue">{systemInfo.version}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="构建时间">
                <Text code>{systemInfo.buildTime}</Text>
              </Descriptions.Item>
              <Descriptions.Item label="运行时间">
                <Space>
                  <ClockCircleOutlined />
                  {systemInfo.uptime}
                </Space>
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Row gutter={16}>
              <Col span={8}>
                <Statistic
                  title="用户数量"
                  value={systemInfo.totalUsers}
                  prefix={<TeamOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="消息总数"
                  value={systemInfo.totalMessages}
                  prefix={<ApiOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="关键词数"
                  value={systemInfo.totalKeywords}
                  prefix={<DatabaseOutlined />}
                />
              </Col>
            </Row>
          </Card>
        </Col>

        {/* 功能特性 */}
        <Col xs={24} lg={12}>
          <Card
            title={
              <Space>
                <HeartOutlined />
                功能特性
              </Space>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              {features.map((feature, index) => (
                <div key={index} style={{ display: 'flex', alignItems: 'flex-start' }}>
                  <div style={{ marginRight: '12px', marginTop: '4px' }}>
                    {feature.icon}
                  </div>
                  <div style={{ flex: 1 }}>
                    <Text strong>{feature.title}</Text>
                    <br />
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {feature.description}
                    </Text>
                  </div>
                </div>
              ))}
            </Space>
          </Card>
        </Col>

        {/* 技术栈 */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <GithubOutlined />
                技术栈
              </Space>
            }
          >
            <Space wrap>
              {techStack.map((tech, index) => (
                <Tag
                  key={index}
                  color={tech.color}
                  style={{
                    padding: '4px 12px',
                    fontSize: '14px',
                    borderRadius: '6px'
                  }}
                >
                  {tech.name} {tech.version}
                </Tag>
              ))}
            </Space>
          </Card>
        </Col>

        {/* 项目信息 */}
        <Col xs={24}>
          <Card
            title={
              <Space>
                <InfoCircleOutlined />
                项目信息
              </Space>
            }
          >
            <Row gutter={[24, 24]}>
              <Col xs={24} md={12}>
                <Paragraph>
                  <Title level={4}>项目简介</Title>
                  <Text>
                    闲鱼自动回复系统是一个基于关键词匹配的智能客服系统，
                    专为闲鱼平台设计，帮助用户自动回复买家咨询，提高交易效率。
                  </Text>
                </Paragraph>

                <Paragraph>
                  <Title level={4}>主要功能</Title>
                  <ul>
                    <li>智能关键词匹配</li>
                    <li>自动回复消息</li>
                    <li>商品信息管理</li>
                    <li>用户权限控制</li>
                    <li>系统日志记录</li>
                  </ul>
                </Paragraph>
              </Col>

              <Col xs={24} md={12}>
                <Alert
                  message="开源项目"
                  description={
                    <div>
                      <Paragraph style={{ marginBottom: '12px' }}>
                        本项目采用开源协议，欢迎贡献代码和提出建议。
                      </Paragraph>
                      <Space>
                        <Button
                          type="primary"
                          icon={<GithubOutlined />}
                          href="https://github.com/your-repo"
                          target="_blank"
                        >
                          查看源码
                        </Button>
                        <Button
                          icon={<BugOutlined />}
                          href="https://github.com/your-repo/issues"
                          target="_blank"
                        >
                          反馈问题
                        </Button>
                      </Space>
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default About;
