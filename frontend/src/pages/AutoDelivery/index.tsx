import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Table,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Statistic,
  Row,
  Col,
  Alert,
} from 'antd';
import {
  TruckOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  CreditCardOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons';
import { deliveryApi, cardApi } from '../../services/api';
import type { DeliveryRule, Card as CardType } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;

const AutoDelivery: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [rules, setRules] = useState<DeliveryRule[]>([]);
  const [cards, setCards] = useState<CardType[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRule, setEditingRule] = useState<DeliveryRule | null>(null);
  const [form] = Form.useForm();

  // 加载发货规则列表
  const loadRules = async () => {
    try {
      setLoading(true);
      const response = await deliveryApi.getDeliveryRules();
      if (response.success) {
        setRules(response.data || []);
      }
    } catch (error: any) {
      console.error('加载发货规则失败:', error);
      message.error('加载发货规则失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载卡券列表
  const loadCards = async () => {
    try {
      const response = await cardApi.getCards();
      if (response.success) {
        setCards(response.data || []);
      }
    } catch (error) {
      console.error('加载卡券列表失败:', error);
    }
  };

  // 添加发货规则
  const handleAddRule = async (values: any) => {
    try {
      const selectedCard = cards.find(card => card.id === values.card_id);
      const ruleData = {
        ...values,
        card_name: selectedCard?.name || '',
        card_type: selectedCard?.type || '',
        delivered_count: 0,
      };

      const response = await deliveryApi.addDeliveryRule(ruleData);
      if (response.success) {
        message.success('发货规则添加成功');
        setModalVisible(false);
        form.resetFields();
        loadRules();
      }
    } catch (error: any) {
      message.error(error.message || '添加发货规则失败');
    }
  };

  // 编辑发货规则
  const handleEditRule = async (values: any) => {
    if (!editingRule) return;

    try {
      const selectedCard = cards.find(card => card.id === values.card_id);
      const ruleData = {
        ...values,
        card_name: selectedCard?.name || '',
        card_type: selectedCard?.type || '',
      };

      const response = await deliveryApi.updateDeliveryRule(editingRule.id, ruleData);
      if (response.success) {
        message.success('发货规则更新成功');
        setModalVisible(false);
        setEditingRule(null);
        form.resetFields();
        loadRules();
      }
    } catch (error: any) {
      message.error(error.message || '更新发货规则失败');
    }
  };

  // 删除发货规则
  const handleDeleteRule = async (id: string) => {
    try {
      const response = await deliveryApi.deleteDeliveryRule(id);
      if (response.success) {
        message.success('发货规则删除成功');
        loadRules();
      }
    } catch (error: any) {
      message.error(error.message || '删除发货规则失败');
    }
  };

  // 打开编辑模态框
  const openEditModal = (rule: DeliveryRule) => {
    setEditingRule(rule);
    form.setFieldsValue(rule);
    setModalVisible(true);
  };

  useEffect(() => {
    loadRules();
    loadCards();
  }, []);

  // 统计数据
  const stats = {
    total: rules.length,
    active: rules.filter(rule => rule.status).length,
    totalDelivered: rules.reduce((sum, rule) => sum + rule.delivered_count, 0),
    totalCapacity: rules.reduce((sum, rule) => sum + rule.delivery_count, 0),
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <TruckOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              自动发货
            </Title>
            <Text type="secondary">配置关键词匹配的自动发货规则</Text>
          </div>
          <div className="header-extra">
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setModalVisible(true)}
              >
                添加规则
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadRules}
                loading={loading}
              >
                刷新
              </Button>
            </Space>
          </div>
        </div>
      </div>

      {/* 统计信息 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总规则数"
              value={stats.total}
              prefix={<TruckOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="启用规则"
              value={stats.active}
              prefix={<CheckCircleOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已发货数"
              value={stats.totalDelivered}
              prefix={<CreditCardOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总容量"
              value={stats.totalCapacity}
              prefix={<ClockCircleOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 发货规则列表 */}
      <Card
        title={
          <Space>
            <TruckOutlined />
            <span>发货规则列表</span>
          </Space>
        }
      >
        <Alert
          message="自动发货说明"
          description="当订单的商品标题或详情包含指定关键词时，系统会自动从对应卡券中获取数据并发货。请确保卡券有足够的库存。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={[
            {
              title: '关键词',
              dataIndex: 'keyword',
              key: 'keyword',
              width: '15%',
              render: (text: string) => (
                <Tag color="blue">{text}</Tag>
              ),
            },
            {
              title: '卡券名称',
              dataIndex: 'card_name',
              key: 'card_name',
              width: '15%',
            },
            {
              title: '卡券类型',
              dataIndex: 'card_type',
              key: 'card_type',
              width: '10%',
              render: (type: string) => {
                const typeConfig = {
                  api: { color: 'blue', text: 'API' },
                  text: { color: 'green', text: '固定文字' },
                  data: { color: 'orange', text: '批量数据' },
                };
                const config = typeConfig[type as keyof typeof typeConfig] || { color: 'default', text: type };
                return <Tag color={config.color}>{config.text}</Tag>;
              },
            },
            {
              title: '发货数量',
              dataIndex: 'delivery_count',
              key: 'delivery_count',
              width: '10%',
              render: (count: number) => (
                <Text strong>{count}</Text>
              ),
            },
            {
              title: '已发货',
              dataIndex: 'delivered_count',
              key: 'delivered_count',
              width: '10%',
              render: (count: number) => (
                <Text type={count > 0 ? 'success' : 'secondary'}>{count}</Text>
              ),
            },
            {
              title: '进度',
              key: 'progress',
              width: '15%',
              render: (_, record: DeliveryRule) => {
                const percentage = record.delivery_count > 0
                  ? Math.round((record.delivered_count / record.delivery_count) * 100)
                  : 0;
                return (
                  <div>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      {record.delivered_count}/{record.delivery_count}
                    </Text>
                    <div style={{
                      width: '100%',
                      height: 4,
                      backgroundColor: '#f0f0f0',
                      borderRadius: 2,
                      marginTop: 4,
                      overflow: 'hidden'
                    }}>
                      <div style={{
                        width: `${percentage}%`,
                        height: '100%',
                        backgroundColor: percentage >= 100 ? '#ff4d4f' : '#52c41a',
                        transition: 'width 0.3s ease'
                      }} />
                    </div>
                  </div>
                );
              },
            },
            {
              title: '状态',
              dataIndex: 'status',
              key: 'status',
              width: '8%',
              render: (status: boolean) => (
                <Tag color={status ? 'success' : 'default'}>
                  {status ? '启用' : '禁用'}
                </Tag>
              ),
            },
            {
              title: '创建时间',
              dataIndex: 'created_at',
              key: 'created_at',
              width: '12%',
              render: (time: string) => (
                <Text type="secondary">
                  {dayjs(time).format('MM-DD HH:mm')}
                </Text>
              ),
            },
            {
              title: '操作',
              key: 'action',
              width: '10%',
              render: (_, record: DeliveryRule) => (
                <Space size="small">
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => openEditModal(record)}
                  />
                  <Popconfirm
                    title="确定要删除这个发货规则吗？"
                    onConfirm={() => handleDeleteRule(record.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                    />
                  </Popconfirm>
                </Space>
              ),
            },
          ]}
          dataSource={rules}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
          locale={{
            emptyText: (
              <div style={{ padding: '40px 0', textAlign: 'center' }}>
                <TruckOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
                <div>
                  <Title level={4} type="secondary">暂无发货规则</Title>
                  <Text type="secondary">点击"添加规则"开始配置自动发货</Text>
                </div>
              </div>
            ),
          }}
        />
      </Card>

      {/* 添加/编辑发货规则模态框 */}
      <Modal
        title={
          <Space>
            {editingRule ? <EditOutlined /> : <PlusOutlined />}
            <span>{editingRule ? '编辑发货规则' : '添加发货规则'}</span>
          </Space>
        }
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingRule(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Alert
          message="发货规则说明"
          description="当订单的商品标题或详情包含指定关键词时，系统会自动从对应卡券中获取指定数量的数据并发货。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={editingRule ? handleEditRule : handleAddRule}
        >
          <Form.Item
            label="匹配关键词"
            name="keyword"
            rules={[{ required: true, message: '请输入匹配关键词' }]}
            help="当订单商品标题或详情包含此关键词时触发自动发货"
          >
            <Input placeholder="例如：会员卡" />
          </Form.Item>

          <Form.Item
            label="选择卡券"
            name="card_id"
            rules={[{ required: true, message: '请选择卡券' }]}
            help="选择用于自动发货的卡券数据源"
          >
            <Select placeholder="选择卡券">
              {cards.map(card => (
                <Option key={card.id} value={card.id}>
                  <Space>
                    <span>{card.name}</span>
                    <Tag color="blue" size="small">{card.type}</Tag>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      库存: {card.data_count}
                    </Text>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="发货数量"
                name="delivery_count"
                rules={[{ required: true, message: '请输入发货数量' }]}
                help="每次匹配到关键词时发货的数量"
              >
                <Input type="number" min={1} placeholder="1" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="规则状态"
                name="status"
                valuePropName="checked"
                help="是否启用此发货规则"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingRule ? '更新' : '添加'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AutoDelivery;
