import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Input,
  Select,
  Button,
  Table,
  Space,
  Tag,
  Row,
  Col,
  Statistic,
  Alert,
  Empty,
} from 'antd';
import {
  SearchOutlined,
  ShoppingOutlined,
  ReloadOutlined,
  FilterOutlined,
  ClearOutlined,
} from '@ant-design/icons';
import { itemApi, accountApi } from '../../services/api';
import type { Item, Account } from '../../types';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { Search } = Input;

const ItemSearch: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [items, setItems] = useState<Item[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedAccount, setSelectedAccount] = useState<string>('');
  const [priceRange, setPriceRange] = useState<string>('');
  const [multiSpec, setMultiSpec] = useState<string>('');

  // 加载账号列表
  const loadAccounts = async () => {
    try {
      const response = await accountApi.getAccounts();
      if (response.success) {
        setAccounts(response.data || []);
      }
    } catch (error) {
      console.error('加载账号列表失败:', error);
    }
  };

  // 搜索商品
  const handleSearch = async () => {
    try {
      setLoading(true);
      const params = {
        keyword: searchKeyword,
        cookieId: selectedAccount,
      };

      const response = await itemApi.getItems(params);
      if (response.success) {
        let filteredItems = response.data?.items || [];

        // 价格筛选
        if (priceRange) {
          const [min, max] = priceRange.split('-').map(Number);
          filteredItems = filteredItems.filter(item => {
            const price = parseFloat(item.price);
            if (max) {
              return price >= min && price <= max;
            } else {
              return price >= min;
            }
          });
        }

        // 规格筛选
        if (multiSpec) {
          const hasMultiSpec = multiSpec === 'true';
          filteredItems = filteredItems.filter(item => item.multi_spec === hasMultiSpec);
        }

        setItems(filteredItems);
      }
    } catch (error: any) {
      console.error('搜索商品失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 清空筛选
  const handleClearFilters = () => {
    setSearchKeyword('');
    setSelectedAccount('');
    setPriceRange('');
    setMultiSpec('');
    setItems([]);
  };

  useEffect(() => {
    loadAccounts();
  }, []);

  // 统计数据
  const stats = {
    total: items.length,
    multiSpec: items.filter(item => item.multi_spec).length,
    multiQuantity: items.filter(item => item.multi_quantity_delivery).length,
    avgPrice: items.length > 0
      ? (items.reduce((sum, item) => sum + parseFloat(item.price), 0) / items.length).toFixed(2)
      : '0',
  };

  return (
    <div>
      {/* 页面头部 */}
      <div className="page-header">
        <div className="header-content">
          <div className="header-info">
            <Title level={2} style={{ margin: 0, display: 'flex', alignItems: 'center' }}>
              <SearchOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              商品搜索
            </Title>
            <Text type="secondary">搜索和筛选所有账号的商品信息</Text>
          </div>
        </div>
      </div>

      {/* 搜索筛选区域 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} md={8}>
            <div>
              <Text strong>搜索关键词</Text>
              <Search
                placeholder="搜索商品标题或详情..."
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                onSearch={handleSearch}
                style={{ marginTop: 4 }}
                enterButton={<SearchOutlined />}
              />
            </div>
          </Col>

          <Col xs={24} md={4}>
            <div>
              <Text strong>筛选账号</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="所有账号"
                allowClear
                value={selectedAccount || undefined}
                onChange={(value) => setSelectedAccount(value || '')}
              >
                {accounts.map(account => (
                  <Option key={account.id} value={account.id}>
                    {account.id}
                  </Option>
                ))}
              </Select>
            </div>
          </Col>

          <Col xs={24} md={4}>
            <div>
              <Text strong>价格范围</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="所有价格"
                allowClear
                value={priceRange || undefined}
                onChange={(value) => setPriceRange(value || '')}
              >
                <Option value="0-50">0-50元</Option>
                <Option value="50-100">50-100元</Option>
                <Option value="100-200">100-200元</Option>
                <Option value="200-500">200-500元</Option>
                <Option value="500">500元以上</Option>
              </Select>
            </div>
          </Col>

          <Col xs={24} md={4}>
            <div>
              <Text strong>商品规格</Text>
              <Select
                style={{ width: '100%', marginTop: 4 }}
                placeholder="所有规格"
                allowClear
                value={multiSpec || undefined}
                onChange={(value) => setMultiSpec(value || '')}
              >
                <Option value="true">多规格</Option>
                <Option value="false">单规格</Option>
              </Select>
            </div>
          </Col>

          <Col xs={24} md={4}>
            <div>
              <Text strong>操作</Text>
              <div style={{ marginTop: 4 }}>
                <Space>
                  <Button
                    type="primary"
                    icon={<SearchOutlined />}
                    onClick={handleSearch}
                    loading={loading}
                  >
                    搜索
                  </Button>
                  <Button
                    icon={<ClearOutlined />}
                    onClick={handleClearFilters}
                  >
                    清空
                  </Button>
                </Space>
              </div>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 统计信息 */}
      {items.length > 0 && (
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="搜索结果"
                value={stats.total}
                prefix={<ShoppingOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="多规格商品"
                value={stats.multiSpec}
                prefix={<FilterOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="多数量发货"
                value={stats.multiQuantity}
                prefix={<ReloadOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均价格"
                value={stats.avgPrice}
                prefix="¥"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索结果 */}
      <Card
        title={
          <Space>
            <ShoppingOutlined />
            <span>搜索结果</span>
            {items.length > 0 && (
              <Tag color="blue">{items.length} 个商品</Tag>
            )}
          </Space>
        }
      >
        {items.length === 0 && !loading ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description={
              <div>
                <Text type="secondary">暂无搜索结果</Text>
                <br />
                <Text type="secondary" style={{ fontSize: 12 }}>
                  请输入关键词并点击搜索按钮
                </Text>
              </div>
            }
          />
        ) : (
          <Table
            columns={[
              {
                title: '账号ID',
                dataIndex: 'cookie_id',
                key: 'cookie_id',
                width: '10%',
                render: (text: string) => (
                  <Text code>{text}</Text>
                ),
              },
              {
                title: '商品ID',
                dataIndex: 'item_id',
                key: 'item_id',
                width: '12%',
                render: (text: string) => (
                  <Text code copyable={{ text }}>
                    {text}
                  </Text>
                ),
              },
              {
                title: '商品标题',
                dataIndex: 'title',
                key: 'title',
                width: '25%',
                render: (text: string) => (
                  <Text ellipsis={{ tooltip: text }}>
                    {text}
                  </Text>
                ),
              },
              {
                title: '商品详情',
                dataIndex: 'detail',
                key: 'detail',
                width: '20%',
                render: (text: string) => (
                  <Text ellipsis={{ tooltip: text }}>
                    {text}
                  </Text>
                ),
              },
              {
                title: '价格',
                dataIndex: 'price',
                key: 'price',
                width: '8%',
                sorter: (a: Item, b: Item) => parseFloat(a.price) - parseFloat(b.price),
                render: (text: string) => (
                  <Text strong style={{ color: '#f50' }}>
                    ¥{text}
                  </Text>
                ),
              },
              {
                title: '规格',
                dataIndex: 'multi_spec',
                key: 'multi_spec',
                width: '8%',
                filters: [
                  { text: '多规格', value: true },
                  { text: '单规格', value: false },
                ],
                onFilter: (value: any, record: Item) => record.multi_spec === value,
                render: (value: boolean) => (
                  <Tag color={value ? 'success' : 'default'}>
                    {value ? '多规格' : '单规格'}
                  </Tag>
                ),
              },
              {
                title: '多数量发货',
                dataIndex: 'multi_quantity_delivery',
                key: 'multi_quantity_delivery',
                width: '10%',
                filters: [
                  { text: '支持', value: true },
                  { text: '不支持', value: false },
                ],
                onFilter: (value: any, record: Item) => record.multi_quantity_delivery === value,
                render: (value: boolean) => (
                  <Tag color={value ? 'processing' : 'default'}>
                    {value ? '支持' : '不支持'}
                  </Tag>
                ),
              },
              {
                title: '更新时间',
                dataIndex: 'updated_at',
                key: 'updated_at',
                width: '12%',
                sorter: (a: Item, b: Item) =>
                  new Date(a.updated_at).getTime() - new Date(b.updated_at).getTime(),
                render: (time: string) => (
                  <Text type="secondary">
                    {dayjs(time).format('MM-DD HH:mm')}
                  </Text>
                ),
              },
            ]}
            dataSource={items}
            rowKey="id"
            loading={loading}
            pagination={{
              pageSize: 20,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `显示第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
            }}
            scroll={{ x: 1000 }}
            size="middle"
          />
        )}
      </Card>
    </div>
  );
};

export default ItemSearch;
