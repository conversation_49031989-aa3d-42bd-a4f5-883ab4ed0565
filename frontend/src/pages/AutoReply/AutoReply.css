/* 自动回复页面样式 */
.auto-reply-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* 账号选择器样式 */
.account-selector-card {
  border: 1px solid #e8f4fd;
  background: linear-gradient(135deg, #f6f9fc 0%, #e8f4fd 100%);
}

.account-selector-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.selector-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

/* 关键词输入区域样式 */
.keyword-input-area {
  padding: 20px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

/* 关键词卡片样式 */
.keyword-card {
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  height: 100%;
}

.keyword-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #1890ff;
  transform: translateY(-2px);
}

.keyword-card .ant-card-head {
  padding: 12px 16px;
  min-height: auto;
}

.keyword-card .ant-card-body {
  padding: 12px 16px;
}

/* 关键词列表样式 */
.keywords-list {
  min-height: 200px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .account-selector-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .keyword-input-area {
    padding: 16px;
  }
  
  .keyword-card {
    margin-bottom: 16px;
  }
}

/* 关键词类型标签样式 */
.ant-tag {
  border-radius: 4px;
  font-weight: 500;
}

/* 图片预览样式 */
.keyword-image-preview {
  border-radius: 6px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.keyword-image-preview:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.2);
}

/* 空状态样式 */
.empty-state {
  color: #999;
}

.empty-state .anticon {
  display: block;
  margin-bottom: 16px;
}

/* 加载状态样式 */
.ant-spin-container {
  min-height: 200px;
}

/* 模态框样式调整 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 上传组件样式 */
.ant-upload-select-picture-card {
  width: 100%;
  height: 120px;
}

.ant-upload-list-picture-card .ant-upload-list-item {
  width: 100%;
  height: 120px;
}

/* 表单样式优化 */
.ant-form-item-explain {
  font-size: 12px;
  color: #666;
}

.ant-form-item-label > label {
  font-weight: 500;
}

/* 按钮组样式 */
.ant-space-item .ant-btn {
  transition: all 0.3s ease;
}

.ant-space-item .ant-btn:hover {
  transform: translateY(-1px);
}

/* 标签样式优化 */
.ant-tag.ant-tag-orange {
  background: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.ant-tag.ant-tag-purple {
  background: #f9f0ff;
  border-color: #d3adf7;
  color: #722ed1;
}

/* 工具提示样式 */
.ant-tooltip-inner {
  max-width: 300px;
}

/* 选择器样式优化 */
.ant-select-selector {
  transition: all 0.3s ease;
}

.ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 输入框样式优化 */
.ant-input:focus,
.ant-input-focused {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 文本域样式 */
.ant-input {
  transition: all 0.3s ease;
}

/* 卡片头部样式 */
.ant-card-head-title {
  font-weight: 600;
}

/* 徽章样式 */
.ant-badge-count {
  font-size: 12px;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
}

/* 分割线样式 */
.ant-divider {
  margin: 16px 0;
}

/* 警告框样式 */
.ant-alert {
  border-radius: 6px;
}

.ant-alert-info {
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

/* 进度条样式 */
.ant-progress-line {
  margin-bottom: 8px;
}

/* 列表项样式 */
.ant-list-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.ant-list-item:last-child {
  border-bottom: none;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.keyword-card {
  animation: fadeInUp 0.3s ease;
}

/* 滚动条样式 */
.keywords-list::-webkit-scrollbar {
  width: 6px;
}

.keywords-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.keywords-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.keywords-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
