import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      // API路径代理 - 统一使用/api前缀
      '/api': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
        // 不需要重写路径，因为后端现在使用/api前缀
      },
      // 静态文件代理
      '/static': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
      // 图片文件代理
      '/images': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
    },
  },
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: false,
  },
})
